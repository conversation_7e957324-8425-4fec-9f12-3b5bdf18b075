% The code has been taken from the study:
%'Multiobjective cuckoo search for design optimization <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>'. 
% Coded by <PERSON><PERSON><PERSON> on Nov 13 2015.
 
% Input parameters
% n     -> Number of steps 
% m     -> Number of Dimensions 
% beta  -> Power law index  % Note: 1 < beta < 2
% Output 
% z     -> 'n' levy steps in 'm' dimension
%_________________________________________________________________________
%  Marine Predators Algorithm source code (Developed in MATLAB R2015a)
%
%  programming: <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>
%
% paper:
%  <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, 
%  Marine Predators Algorithm: A Nature-inspired Metaheuristic
%  Expert Systems with Applications
%  DOI: doi.org/10.1016/j.eswa.2020.113377
%  
%  E-mails: a<PERSON><PERSON><PERSON>@hawk.iit.edu            (<PERSON><PERSON><PERSON>)
%           <EMAIL>                   (<PERSON>)
%           ali.mir<PERSON><PERSON><PERSON>@laureate.edu.au    (<PERSON><PERSON><PERSON>) 
%           <EMAIL>               (Amir H Gandomi)
%_________________________________________________________________________

function [z] = levy(n,m,beta)

    num = gamma(1+beta)*sin(pi*beta/2); % used for Numerator 
    
    den = gamma((1+beta)/2)*beta*2^((beta-1)/2); % used for Denominator

    sigma_u = (num/den)^(1/beta);% Standard deviation

    u = random('Normal',0,sigma_u,n,m); 
    
    v = random('Normal',0,1,n,m);

    z =u./(abs(v).^(1/beta));
  
end